import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  ScrollView,
  Dimensions,
  Alert,
  Modal,
  TextInput
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import BrowserWebView from './BrowserWebView';
import TabGroup, { TAB_GROUP_COLORS } from './TabGroup';

const { width } = Dimensions.get('window');

const TabBrowser = ({ onVideoDetected, onDownloadRequest }) => {
  const [tabs, setTabs] = useState([
    {
      id: '1',
      title: 'Nova Aba',
      url: 'https://www.google.com',
      isActive: true,
      groupId: null
    }
  ]);
  const [activeTabId, setActiveTabId] = useState('1');
  const [tabGroups, setTabGroups] = useState([]);
  const [showGroupModal, setShowGroupModal] = useState(false);
  const [selectedTabsForGroup, setSelectedTabsForGroup] = useState([]);
  const [newGroupName, setNewGroupName] = useState('');
  const [showTabMenu, setShowTabMenu] = useState(false);
  const [selectedTabId, setSelectedTabId] = useState(null);

  const createNewTab = (url = 'https://www.google.com', groupId = null) => {
    const newTab = {
      id: Date.now().toString(),
      title: 'Nova Aba',
      url: url,
      isActive: false,
      groupId: groupId
    };

    setTabs(prevTabs => [...prevTabs, newTab]);
    switchToTab(newTab.id);
  };

  const closeTab = (tabId) => {
    if (tabs.length === 1) {
      // Se for a última aba, criar uma nova
      createNewTab();
      return;
    }

    const tabIndex = tabs.findIndex(tab => tab.id === tabId);
    const newTabs = tabs.filter(tab => tab.id !== tabId);
    
    // Se a aba fechada era a ativa, ativar outra
    if (tabId === activeTabId) {
      const newActiveIndex = tabIndex > 0 ? tabIndex - 1 : 0;
      const newActiveTab = newTabs[newActiveIndex];
      setActiveTabId(newActiveTab.id);
    }
    
    setTabs(newTabs);
  };

  const switchToTab = (tabId) => {
    setActiveTabId(tabId);
    setTabs(prevTabs => 
      prevTabs.map(tab => ({
        ...tab,
        isActive: tab.id === tabId
      }))
    );
  };

  const updateTabTitle = (tabId, title) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.id === tabId ? { ...tab, title: title || 'Carregando...' } : tab
      )
    );
  };

  const updateTabUrl = (tabId, url) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.id === tabId ? { ...tab, url } : tab
      )
    );
  };

  const getTabTitle = (title, url) => {
    if (title && title !== 'Carregando...' && title !== 'Nova Aba') {
      return title.length > 15 ? title.substring(0, 15) + '...' : title;
    }

    if (url) {
      try {
        const domain = new URL(url).hostname.replace('www.', '');
        return domain.length > 15 ? domain.substring(0, 15) + '...' : domain;
      } catch {
        return 'Nova Aba';
      }
    }

    return 'Nova Aba';
  };

  // Tab Group Management Functions
  const createTabGroup = (name, color, tabIds) => {
    const newGroup = {
      id: Date.now().toString(),
      name: name || 'Novo Grupo',
      color: color || TAB_GROUP_COLORS[0].color,
      collapsed: false
    };

    setTabGroups(prev => [...prev, newGroup]);

    // Assign tabs to the group
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tabIds.includes(tab.id) ? { ...tab, groupId: newGroup.id } : tab
      )
    );

    return newGroup.id;
  };

  const addTabToGroup = (tabId, groupId) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.id === tabId ? { ...tab, groupId } : tab
      )
    );
  };

  const removeTabFromGroup = (tabId) => {
    setTabs(prevTabs =>
      prevTabs.map(tab =>
        tab.id === tabId ? { ...tab, groupId: null } : tab
      )
    );
  };

  const toggleGroupCollapse = (groupId) => {
    setTabGroups(prev =>
      prev.map(group =>
        group.id === groupId ? { ...group, collapsed: !group.collapsed } : group
      )
    );
  };

  const renameGroup = (groupId, newName) => {
    setTabGroups(prev =>
      prev.map(group =>
        group.id === groupId ? { ...group, name: newName } : group
      )
    );
  };

  const changeGroupColor = (groupId, newColor) => {
    setTabGroups(prev =>
      prev.map(group =>
        group.id === groupId ? { ...group, color: newColor } : group
      )
    );
  };

  const deleteGroup = (groupId) => {
    Alert.alert(
      'Excluir Grupo',
      'Tem certeza que deseja excluir este grupo? As abas não serão fechadas.',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Excluir',
          style: 'destructive',
          onPress: () => {
            // Remove group
            setTabGroups(prev => prev.filter(group => group.id !== groupId));
            // Remove group assignment from tabs
            setTabs(prevTabs =>
              prevTabs.map(tab =>
                tab.groupId === groupId ? { ...tab, groupId: null } : tab
              )
            );
          }
        }
      ]
    );
  };

  const handleCreateGroup = () => {
    if (selectedTabsForGroup.length === 0) {
      Alert.alert('Erro', 'Selecione pelo menos uma aba para criar um grupo');
      return;
    }

    const groupName = newGroupName.trim() || 'Novo Grupo';
    createTabGroup(groupName, TAB_GROUP_COLORS[0].color, selectedTabsForGroup);

    setShowGroupModal(false);
    setSelectedTabsForGroup([]);
    setNewGroupName('');
  };

  const handleTabLongPress = (tabId) => {
    setSelectedTabId(tabId);
    setShowTabMenu(true);
  };

  const activeTab = tabs.find(tab => tab.id === activeTabId);
  const ungroupedTabs = tabs.filter(tab => !tab.groupId);

  return (
    <View style={styles.container}>
      {/* Barra de abas com safe area */}
      <SafeAreaView edges={['left', 'right']} style={styles.tabBarContainer}>
        <View style={styles.tabBarHeader}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.tabsContainer}
            contentContainerStyle={styles.tabsContent}
          >
            {/* Ungrouped tabs */}
            {ungroupedTabs.map((tab) => (
              <TouchableOpacity
                key={tab.id}
                style={[
                  styles.tab,
                  tab.id === activeTabId && styles.activeTab
                ]}
                onPress={() => switchToTab(tab.id)}
                onLongPress={() => handleTabLongPress(tab.id)}
              >
                <Text style={[
                  styles.tabTitle,
                  tab.id === activeTabId && styles.activeTabTitle
                ]}>
                  {getTabTitle(tab.title, tab.url)}
                </Text>

                <TouchableOpacity
                  style={styles.closeTabButton}
                  onPress={() => closeTab(tab.id)}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Ionicons name="close" size={14} color="#888" />
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </ScrollView>

          <View style={styles.tabActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => setShowGroupModal(true)}
            >
              <Ionicons name="folder" size={18} color="#fff" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.newTabButton}
              onPress={() => createNewTab()}
            >
              <Ionicons name="add" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Tab Groups */}
        {tabGroups.length > 0 && (
          <ScrollView style={styles.groupsContainer}>
            {tabGroups.map((group) => (
              <TabGroup
                key={group.id}
                group={group}
                tabs={tabs}
                onToggleCollapse={toggleGroupCollapse}
                onRenameGroup={renameGroup}
                onChangeGroupColor={changeGroupColor}
                onDeleteGroup={deleteGroup}
                onTabPress={switchToTab}
                onCloseTab={closeTab}
                activeTabId={activeTabId}
              />
            ))}
          </ScrollView>
        )}
      </SafeAreaView>

      {/* Conteúdo da aba ativa */}
      {activeTab && (
        <BrowserWebView
          key={activeTab.id}
          initialUrl={activeTab.url}
          onVideoDetected={onVideoDetected}
          onDownloadRequest={onDownloadRequest}
          onTitleChange={(title) => updateTabTitle(activeTab.id, title)}
          onUrlChange={(url) => updateTabUrl(activeTab.id, url)}
        />
      )}

      {/* Create Group Modal */}
      <Modal
        visible={showGroupModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowGroupModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Criar Grupo de Abas</Text>

            <TextInput
              style={styles.modalInput}
              value={newGroupName}
              onChangeText={setNewGroupName}
              placeholder="Nome do grupo"
              placeholderTextColor="#888"
              autoFocus
            />

            <Text style={styles.sectionTitle}>Selecionar Abas:</Text>
            <ScrollView style={styles.tabSelectionList}>
              {ungroupedTabs.map((tab) => (
                <TouchableOpacity
                  key={tab.id}
                  style={[
                    styles.tabSelectionItem,
                    selectedTabsForGroup.includes(tab.id) && styles.selectedTabItem
                  ]}
                  onPress={() => {
                    setSelectedTabsForGroup(prev =>
                      prev.includes(tab.id)
                        ? prev.filter(id => id !== tab.id)
                        : [...prev, tab.id]
                    );
                  }}
                >
                  <View style={styles.tabSelectionContent}>
                    <Text style={styles.tabSelectionTitle}>
                      {tab.title || 'Nova Aba'}
                    </Text>
                    <Text style={styles.tabSelectionUrl}>
                      {tab.url}
                    </Text>
                  </View>
                  {selectedTabsForGroup.includes(tab.id) && (
                    <Ionicons name="checkmark-circle" size={20} color="#6c5ce7" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowGroupModal(false);
                  setSelectedTabsForGroup([]);
                  setNewGroupName('');
                }}
              >
                <Text style={styles.cancelButtonText}>Cancelar</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleCreateGroup}
              >
                <Text style={styles.confirmButtonText}>Criar Grupo</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Tab Context Menu */}
      <Modal
        visible={showTabMenu}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowTabMenu(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowTabMenu(false)}
        >
          <View style={styles.contextMenu}>
            <TouchableOpacity
              style={styles.contextMenuItem}
              onPress={() => {
                if (selectedTabId) {
                  setSelectedTabsForGroup([selectedTabId]);
                  setShowTabMenu(false);
                  setShowGroupModal(true);
                }
              }}
            >
              <Ionicons name="folder" size={20} color="#fff" />
              <Text style={styles.contextMenuText}>Adicionar ao Grupo</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.contextMenuItem}
              onPress={() => {
                if (selectedTabId) {
                  closeTab(selectedTabId);
                  setShowTabMenu(false);
                }
              }}
            >
              <Ionicons name="close" size={20} color="#ff4757" />
              <Text style={[styles.contextMenuText, { color: '#ff4757' }]}>Fechar Aba</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  tabBarContainer: {
    backgroundColor: '#1a1a2e',
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
    maxHeight: '40%',
  },
  tabBarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 8,
  },
  groupsContainer: {
    maxHeight: 200,
    paddingHorizontal: 8,
  },
  tabActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginRight: 8,
    backgroundColor: '#2d2d44',
    borderRadius: 6,
  },
  tabsContainer: {
    flex: 1,
  },
  tabsContent: {
    paddingHorizontal: 5,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2d2d44',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 2,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    minWidth: 120,
    maxWidth: 180,
  },
  activeTab: {
    backgroundColor: '#1a1a2e',
    borderBottomWidth: 2,
    borderBottomColor: '#6c5ce7',
  },
  tabTitle: {
    color: '#888',
    fontSize: 12,
    flex: 1,
    marginRight: 8,
  },
  activeTabTitle: {
    color: '#fff',
    fontWeight: 'bold',
  },
  closeTabButton: {
    padding: 2,
  },
  newTabButton: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 8,
    backgroundColor: '#2d2d44',
    marginRight: 5,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 20,
    width: width * 0.9,
    maxHeight: '80%',
  },
  modalTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalInput: {
    backgroundColor: '#2d2d44',
    color: '#fff',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  tabSelectionList: {
    maxHeight: 200,
    marginBottom: 16,
  },
  tabSelectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#2d2d44',
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedTabItem: {
    borderColor: '#6c5ce7',
    backgroundColor: '#3d3d54',
  },
  tabSelectionContent: {
    flex: 1,
  },
  tabSelectionTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  tabSelectionUrl: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#666',
    marginRight: 8,
  },
  confirmButton: {
    backgroundColor: '#6c5ce7',
    marginLeft: 8,
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  contextMenu: {
    backgroundColor: '#1a1a2e',
    borderRadius: 8,
    padding: 8,
    minWidth: 200,
  },
  contextMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 6,
  },
  contextMenuText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 12,
  },
});

export default TabBrowser;
