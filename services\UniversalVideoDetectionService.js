// Universal Video Detection Service
// Comprehensive video thumbnail detection across all platforms

class UniversalVideoDetectionService {
  
  // Platform-specific detection patterns
  static PLATFORM_PATTERNS = {
    // Social Media Platforms
    youtube: {
      domains: ['youtube.com', 'youtu.be', 'youtube-nocookie.com'],
      selectors: [
        'ytd-thumbnail', '.ytd-thumbnail', '#thumbnail',
        '.yt-simple-endpoint', '.ytd-video-preview',
        '.ytd-rich-item-renderer', '.ytd-video-renderer',
        '.ytp-videowall-still', '.video-thumb'
      ],
      urlPatterns: [/\/watch\?v=/, /\/embed\//, /youtu\.be\//],
      titleSelectors: ['#video-title', '.ytd-video-meta-block', 'h3 a'],
      durationSelectors: ['.ytd-thumbnail-overlay-time-status-renderer', '.duration']
    },

    instagram: {
      domains: ['instagram.com'],
      selectors: [
        '._aagv', '._ac7v', '._acat', '._acay',
        '.x1lliihq', '.x1n2onr6', '.x1ja2u2z',
        '[role="button"]', '.video-thumbnail'
      ],
      urlPatterns: [/\/p\//, /\/reel\//, /\/tv\//],
      titleSelectors: ['._aacl', '._aad6', '._aad7'],
      durationSelectors: ['.duration', '.time-indicator']
    },

    tiktok: {
      domains: ['tiktok.com'],
      selectors: [
        '.video-feed-item', '.video-card',
        '[data-e2e="video-card"]', '.video-player-container',
        '.tiktok-avatar', '.video-meta'
      ],
      urlPatterns: [/\/@.*\/video\//, /\/v\//],
      titleSelectors: ['[data-e2e="video-desc"]', '.video-meta-caption'],
      durationSelectors: ['.video-duration', '.time']
    },

    facebook: {
      domains: ['facebook.com', 'fb.com'],
      selectors: [
        '[data-pagelet="FeedUnit"]', '.story_body_container',
        '.uiScaledImageContainer', '.fbStoryAttachmentImage',
        '.video-thumbnail', '.media-object'
      ],
      urlPatterns: [/\/videos\//, /\/watch\//],
      titleSelectors: ['[data-testid="post_message"]', '.userContent'],
      durationSelectors: ['.duration', '.time-indicator']
    },

    twitter: {
      domains: ['twitter.com', 'x.com'],
      selectors: [
        '[data-testid="videoPlayer"]', '[data-testid="tweetPhoto"]',
        '.media-preview', '.video-overlay',
        '.PlayableMedia', '.tweet-media'
      ],
      urlPatterns: [/\/status\//, /\/i\/status\//],
      titleSelectors: ['[data-testid="tweetText"]', '.tweet-text'],
      durationSelectors: ['.duration', '.time-code']
    },

    // Video Streaming Platforms
    vimeo: {
      domains: ['vimeo.com'],
      selectors: [
        '.thumbnail', '.clip-thumbnail', '.video-thumbnail',
        '.player_container', '.vp-preview'
      ],
      urlPatterns: [/\/\d+/, /\/video\//],
      titleSelectors: ['.clip-title', 'h1', '.title'],
      durationSelectors: ['.duration', '.clip-duration']
    },

    twitch: {
      domains: ['twitch.tv'],
      selectors: [
        '.preview-card-thumbnail', '.tw-image',
        '.video-preview-card', '.stream-thumbnail'
      ],
      urlPatterns: [/\/videos\//, /\/clip\//],
      titleSelectors: ['.tw-title', '.video-title'],
      durationSelectors: ['.video-length', '.duration']
    },

    dailymotion: {
      domains: ['dailymotion.com'],
      selectors: [
        '.thumbnail', '.video-thumbnail', '.preview-image',
        '.dmco-video-thumbnail'
      ],
      urlPatterns: [/\/video\//, /\/embed\//],
      titleSelectors: ['.video-title', 'h1'],
      durationSelectors: ['.duration', '.video-duration']
    },

    rumble: {
      domains: ['rumble.com'],
      selectors: [
        '.video-item--thumb', '.thumbnail-container',
        '.video-thumbnail'
      ],
      urlPatterns: [/\/v\//, /\/embed\//],
      titleSelectors: ['.video-item--title', 'h3'],
      durationSelectors: ['.video-item--duration', '.duration']
    },

    // Generic patterns for news sites, blogs, e-commerce
    generic: {
      selectors: [
        // Video containers
        '.video-container', '.video-wrapper', '.video-player',
        '.media-container', '.media-wrapper', '.player-container',
        
        // Thumbnail classes
        '.video-thumbnail', '.video-thumb', '.video-preview',
        '.thumbnail', '.thumb', '.preview-image',
        
        // Player elements
        '.video-player', '.player', '.media-player',
        '.jwplayer', '.vjs-poster', '.plyr__poster',
        
        // Article/content video elements
        '.article-video', '.content-video', '.post-video',
        '.featured-video', '.hero-video', '.banner-video',
        
        // E-commerce product videos
        '.product-video', '.product-media', '.product-gallery',
        '.product-thumbnail', '.gallery-thumbnail',
        
        // Educational platform elements
        '.lesson-video', '.course-video', '.tutorial-video',
        '.lecture-thumbnail', '.video-lesson'
      ],
      urlPatterns: [
        /\/watch/, /\/video/, /\/play/, /\/stream/,
        /\/media/, /\/content/, /\/embed/
      ],
      titleSelectors: [
        'h1', 'h2', 'h3', '.title', '.video-title',
        '.media-title', '.content-title', '[title]'
      ],
      durationSelectors: [
        '.duration', '.time', '.length', '.runtime',
        '.video-duration', '.media-duration'
      ]
    }
  };

  // Enhanced video indicators for universal detection
  static UNIVERSAL_VIDEO_INDICATORS = [
    // Core video terms
    'video', 'player', 'media', 'stream', 'play',
    'thumbnail', 'thumb', 'preview', 'poster',
    
    // Platform-specific terms
    'ytd-', 'vjs-', 'jwplayer', 'plyr', 'videojs',
    'flowplayer', 'brightcove', 'kaltura',
    
    // Content types
    'clip', 'episode', 'movie', 'film', 'documentary',
    'tutorial', 'lesson', 'course', 'lecture',
    'product-demo', 'review', 'trailer',
    
    // UI elements
    'overlay', 'controls', 'scrubber', 'timeline',
    'volume', 'fullscreen', 'captions', 'quality',
    
    // Social media specific
    'story', 'reel', 'short', 'tiktok', 'snap',
    'live', 'broadcast', 'stream'
  ];

  // Video file extensions and MIME types
  static VIDEO_EXTENSIONS = [
    'mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv',
    'flv', 'mkv', '3gp', 'm4v', 'mpg', 'mpeg'
  ];

  static VIDEO_MIME_TYPES = [
    'video/mp4', 'video/webm', 'video/ogg',
    'video/avi', 'video/quicktime', 'video/x-msvideo'
  ];

  // Get platform-specific patterns based on current domain
  static getPlatformPatterns(hostname) {
    const domain = hostname.toLowerCase().replace('www.', '');
    
    for (const [platform, config] of Object.entries(this.PLATFORM_PATTERNS)) {
      if (config.domains && config.domains.some(d => domain.includes(d))) {
        return { platform, ...config };
      }
    }
    
    return { platform: 'generic', ...this.PLATFORM_PATTERNS.generic };
  }

  // Enhanced element detection
  static isVideoThumbnail(element, platformPatterns) {
    if (!element || !element.tagName) return false;

    const elementClasses = element.className?.toLowerCase() || '';
    const elementId = element.id?.toLowerCase() || '';
    const elementTag = element.tagName.toLowerCase();
    
    // Get element hierarchy for context
    const hierarchy = this.getElementHierarchy(element, 3);
    const hierarchyText = hierarchy.map(el => 
      `${el.tagName.toLowerCase()} ${el.className || ''} ${el.id || ''}`
    ).join(' ').toLowerCase();

    // Platform-specific detection
    if (platformPatterns.selectors) {
      for (const selector of platformPatterns.selectors) {
        try {
          if (element.matches(selector) || element.closest(selector)) {
            return true;
          }
        } catch (e) {
          // Ignore invalid selectors
        }
      }
    }

    // Universal indicator detection
    const hasVideoIndicator = this.UNIVERSAL_VIDEO_INDICATORS.some(indicator => 
      elementClasses.includes(indicator) || 
      elementId.includes(indicator) ||
      hierarchyText.includes(indicator)
    );

    // Image-specific checks
    if (elementTag === 'img') {
      const src = element.src?.toLowerCase() || '';
      const alt = element.alt?.toLowerCase() || '';
      
      // Check for video-related image sources
      const hasVideoSrc = [
        'thumbnail', 'preview', 'poster', 'cover',
        'maxresdefault', 'hqdefault', 'mqdefault',
        'vi_webp', 'ytimg.com', 'vimeocdn.com'
      ].some(pattern => src.includes(pattern));

      const hasVideoAlt = [
        'video', 'thumbnail', 'preview', 'play'
      ].some(pattern => alt.includes(pattern));

      if (hasVideoSrc || hasVideoAlt) return true;
    }

    // Video element detection
    if (elementTag === 'video') {
      return true;
    }

    // Container detection
    const isVideoContainer = element.querySelector('video, iframe[src*="youtube"], iframe[src*="vimeo"], iframe[src*="embed"]');
    if (isVideoContainer) return true;

    // Link detection
    const linkElement = element.closest('a') || element.querySelector('a');
    if (linkElement && linkElement.href) {
      const href = linkElement.href.toLowerCase();
      
      // Platform-specific URL patterns
      if (platformPatterns.urlPatterns) {
        const matchesPattern = platformPatterns.urlPatterns.some(pattern => 
          pattern.test(href)
        );
        if (matchesPattern) return true;
      }

      // Generic video URL patterns
      const hasVideoUrl = [
        'watch', 'video', 'play', 'stream', 'embed',
        'youtube.com', 'vimeo.com', 'dailymotion.com'
      ].some(pattern => href.includes(pattern));

      if (hasVideoUrl) return true;
    }

    // Data attribute detection
    const hasVideoData = [
      'data-video-id', 'data-video-url', 'data-embed-url',
      'data-youtube-id', 'data-vimeo-id', 'data-player-id'
    ].some(attr => element.hasAttribute(attr));

    if (hasVideoData) return true;

    return hasVideoIndicator;
  }

  // Get element hierarchy for context analysis
  static getElementHierarchy(element, depth = 3) {
    const hierarchy = [];
    let current = element;
    
    for (let i = 0; i < depth && current; i++) {
      hierarchy.push({
        tagName: current.tagName,
        className: current.className,
        id: current.id
      });
      current = current.parentElement;
    }
    
    return hierarchy;
  }

  // Check if element is likely lazy-loaded
  static isLazyLoaded(element) {
    const lazyAttributes = [
      'data-src', 'data-lazy', 'data-original',
      'data-srcset', 'loading="lazy"'
    ];
    
    return lazyAttributes.some(attr => 
      element.hasAttribute(attr.split('=')[0])
    );
  }

  // Enhanced mutation observer for dynamic content
  static createSmartObserver(callback) {
    let observerTimeout = null;
    let lastMutationTime = 0;
    
    const debouncedCallback = () => {
      if (observerTimeout) clearTimeout(observerTimeout);
      observerTimeout = setTimeout(() => {
        const now = Date.now();
        if (now - lastMutationTime > 100) { // Minimum 100ms between calls
          callback();
          lastMutationTime = now;
        }
      }, 300);
    };

    const observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;
      
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check for video-related additions
              const hasRelevantContent = 
                node.tagName === 'IMG' ||
                node.tagName === 'VIDEO' ||
                node.querySelector?.('img, video, iframe') ||
                this.UNIVERSAL_VIDEO_INDICATORS.some(indicator => 
                  node.className?.toLowerCase().includes(indicator)
                );
              
              if (hasRelevantContent) {
                shouldUpdate = true;
              }
            }
          });
        }
      });
      
      if (shouldUpdate) {
        debouncedCallback();
      }
    });

    return observer;
  }
}

export default UniversalVideoDetectionService;
