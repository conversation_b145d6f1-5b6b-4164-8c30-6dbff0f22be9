import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Dimensions,
  StatusBar,
  Text,
  Modal,
  ScrollView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import VideoThumbnailContextMenu from './VideoThumbnailContextMenu';

const { width, height } = Dimensions.get('window');

const BrowserWebView = ({
  initialUrl = 'https://www.google.com',
  onVideoDetected,
  onDownloadRequest,
  onTitleChange,
  onUrlChange,
  showDownloadButton = true
}) => {
  const [url, setUrl] = useState(initialUrl);
  const [currentUrl, setCurrentUrl] = useState(initialUrl);
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [history, setHistory] = useState([]);
  const [bookmarks, setBookmarks] = useState([]);
  const [showBookmarks, setShowBookmarks] = useState(false);
  const [pageTitle, setPageTitle] = useState('');
  const [isSecure, setIsSecure] = useState(false);
  const [showThumbnailMenu, setShowThumbnailMenu] = useState(false);
  const [thumbnailData, setThumbnailData] = useState(null);

  const webViewRef = useRef(null);

  useEffect(() => {
    loadBookmarks();
  }, []);

  const loadBookmarks = async () => {
    try {
      const savedBookmarks = await AsyncStorage.getItem('browser_bookmarks');
      if (savedBookmarks) {
        setBookmarks(JSON.parse(savedBookmarks));
      }
    } catch (error) {
      console.error('Erro ao carregar favoritos:', error);
    }
  };

  const saveBookmarks = async (newBookmarks) => {
    try {
      await AsyncStorage.setItem('browser_bookmarks', JSON.stringify(newBookmarks));
      setBookmarks(newBookmarks);
    } catch (error) {
      console.error('Erro ao salvar favoritos:', error);
    }
  };

  const addBookmark = () => {
    const newBookmark = {
      id: Date.now().toString(),
      title: pageTitle || currentUrl,
      url: currentUrl,
      timestamp: new Date().toISOString()
    };
    
    const updatedBookmarks = [...bookmarks, newBookmark];
    saveBookmarks(updatedBookmarks);
    Alert.alert('Sucesso', 'Página adicionada aos favoritos!');
  };

  const removeBookmark = (id) => {
    const updatedBookmarks = bookmarks.filter(bookmark => bookmark.id !== id);
    saveBookmarks(updatedBookmarks);
  };

  const navigateToUrl = (targetUrl) => {
    let formattedUrl = targetUrl.trim();
    
    // Adicionar protocolo se necessário
    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      // Se parece ser uma busca, usar Google
      if (!formattedUrl.includes('.') || formattedUrl.includes(' ')) {
        formattedUrl = `https://www.google.com/search?q=${encodeURIComponent(formattedUrl)}`;
      } else {
        formattedUrl = `https://${formattedUrl}`;
      }
    }
    
    setUrl(formattedUrl);
    setCurrentUrl(formattedUrl);
  };

  const handleNavigationStateChange = (navState) => {
    setCurrentUrl(navState.url);
    setCanGoBack(navState.canGoBack);
    setCanGoForward(navState.canGoForward);
    setLoading(navState.loading);
    setPageTitle(navState.title);
    setIsSecure(navState.url.startsWith('https://'));

    // Notificar mudanças para o componente pai (TabBrowser)
    if (onTitleChange) onTitleChange(navState.title);
    if (onUrlChange) onUrlChange(navState.url);

    // Adicionar ao histórico
    if (!loading && navState.url !== currentUrl) {
      setHistory(prev => {
        const newHistory = [...prev, {
          url: navState.url,
          title: navState.title,
          timestamp: new Date().toISOString()
        }];
        return newHistory.slice(-50); // Manter apenas os últimos 50
      });
    }
  };

  const detectVideoInPage = (html) => {
    // Detectar vídeos na página
    const videoPatterns = [
      /\.mp4/gi,
      /\.webm/gi,
      /\.avi/gi,
      /\.mov/gi,
      /youtube\.com\/watch/gi,
      /vimeo\.com/gi,
      /tiktok\.com/gi,
      /instagram\.com.*\/p\//gi,
      /facebook\.com.*\/videos/gi
    ];

    const hasVideo = videoPatterns.some(pattern => pattern.test(html));
    if (hasVideo && onVideoDetected) {
      onVideoDetected(currentUrl);
    }
  };

  const handleMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      if (data.type === 'pageContent') {
        detectVideoInPage(data.content);
      } else if (data.type === 'videoThumbnailLongPress') {
        setThumbnailData(data.thumbnailData);
        setShowThumbnailMenu(true);
      }
    } catch (error) {
      console.error('Erro ao processar mensagem:', error);
    }
  };

  const injectedJavaScript = `
    (function() {
      try {
        // Melhorar renderização da página
        const meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes';
        if (!document.querySelector('meta[name="viewport"]')) {
          document.head.appendChild(meta);
        }

        // Video thumbnail detection and context menu functionality
        let longPressTimer = null;
        let touchStartTime = 0;
        let touchStartPosition = { x: 0, y: 0 };
        let hasMoved = false;
        const LONG_PRESS_DURATION = 600; // 600ms for more reliable long press
        const MOVE_THRESHOLD = 10; // pixels - if touch moves more than this, cancel long press

        // Function to extract video information from thumbnail
        function extractVideoInfo(element) {
          const info = {
            title: null,
            videoUrl: null,
            thumbnailUrl: null,
            duration: null,
            x: 0,
            y: 0
          };

          // Get position
          const rect = element.getBoundingClientRect();
          info.x = rect.left + rect.width / 2;
          info.y = rect.top + rect.height / 2;

          // Enhanced title extraction
          const titleSelectors = [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            '[title]', '[aria-label]', '[alt]',
            '.title', '.video-title', '.video-name', '.video-headline',
            '.yt-simple-endpoint', '.ytd-video-meta-block',
            '.media-heading', '.entry-title', '.post-title',
            '[data-title]', '[data-video-title]'
          ];

          // Search in element and its containers
          const searchContainers = [
            element,
            element.parentElement,
            element.parentElement?.parentElement,
            element.closest('article, .video-item, .media-object, .video-card, .video-container')
          ].filter(Boolean);

          for (const container of searchContainers) {
            for (const selector of titleSelectors) {
              const titleEl = container.querySelector?.(selector);
              if (titleEl) {
                info.title = titleEl.textContent?.trim() ||
                            titleEl.getAttribute('title') ||
                            titleEl.getAttribute('aria-label') ||
                            titleEl.getAttribute('alt') ||
                            titleEl.getAttribute('data-title') ||
                            titleEl.getAttribute('data-video-title');
                if (info.title && info.title.length > 3) break;
              }
            }
            if (info.title && info.title.length > 3) break;
          }

          // Enhanced video URL extraction
          const linkEl = element.closest('a') ||
                        element.querySelector('a') ||
                        element.parentElement?.querySelector('a');

          if (linkEl && linkEl.href) {
            info.videoUrl = linkEl.href;
          } else {
            // Try to find video URL in data attributes
            const videoUrlAttrs = ['data-video-url', 'data-href', 'data-url', 'data-video-id'];
            for (const container of searchContainers) {
              for (const attr of videoUrlAttrs) {
                const url = container?.getAttribute?.(attr);
                if (url) {
                  info.videoUrl = url.startsWith('http') ? url : window.location.origin + url;
                  break;
                }
              }
              if (info.videoUrl) break;
            }
          }

          // Enhanced thumbnail URL extraction
          const imgEl = element.tagName === 'IMG' ? element : element.querySelector('img');
          if (imgEl && imgEl.src) {
            info.thumbnailUrl = imgEl.src;
          }

          // Enhanced duration extraction
          const durationSelectors = [
            '.duration', '.video-duration', '.time', '.length',
            '[data-duration]', '.ytd-thumbnail-overlay-time-status-renderer',
            '.video-time', '.runtime', '.playtime'
          ];

          for (const container of searchContainers) {
            for (const selector of durationSelectors) {
              const durationEl = container?.querySelector?.(selector);
              if (durationEl) {
                info.duration = durationEl.textContent?.trim() ||
                               durationEl.getAttribute('data-duration');
                if (info.duration && info.duration.match(/\d+:\d+/)) break;
              }
            }
            if (info.duration && info.duration.match(/\d+:\d+/)) break;
          }

          // Fallback: use image alt text as title if no title found
          if (!info.title && imgEl?.alt) {
            info.title = imgEl.alt.trim();
          }

          // Fallback: use link text as title
          if (!info.title && linkEl?.textContent) {
            const linkText = linkEl.textContent.trim();
            if (linkText.length > 3 && linkText.length < 200) {
              info.title = linkText;
            }
          }

          return info;
        }

        // Function to check if element is likely a video thumbnail
        function isVideoThumbnail(element) {
          if (!element) return false;

          // Enhanced video-related indicators
          const videoIndicators = [
            'video', 'thumbnail', 'preview', 'player', 'media',
            'ytd-thumbnail', 'video-thumb', 'media-object',
            'video-preview', 'video-item', 'video-card',
            'thumb', 'poster', 'cover', 'featured-image',
            'video-link', 'play-button', 'video-container'
          ];

          const elementClasses = element.className?.toLowerCase() || '';
          const elementId = element.id?.toLowerCase() || '';
          const parentClasses = element.parentElement?.className?.toLowerCase() || '';
          const grandParentClasses = element.parentElement?.parentElement?.className?.toLowerCase() || '';

          // Check element and its parents for video indicators
          const hasVideoIndicator = videoIndicators.some(indicator =>
            elementClasses.includes(indicator) ||
            elementId.includes(indicator) ||
            parentClasses.includes(indicator) ||
            grandParentClasses.includes(indicator)
          );

          // Check if it's an image that might be a video thumbnail
          const isImage = element.tagName === 'IMG';

          // Look for play buttons or video overlays
          const hasPlayButton = element.querySelector('[class*="play"], [class*="overlay"], [class*="duration"]') ||
                               element.parentElement?.querySelector('[class*="play"], [class*="overlay"], [class*="duration"]') ||
                               element.parentElement?.parentElement?.querySelector('[class*="play"], [class*="overlay"], [class*="duration"]');

          // Check if it's inside a video-related container or link
          const videoContainer = element.closest('a[href*="watch"], a[href*="video"], a[href*="/v/"], a[href*="youtube"], a[href*="vimeo"], [class*="video"], [class*="thumbnail"], [class*="media"]');

          // Check for video-specific attributes
          const hasVideoAttributes = element.hasAttribute('data-video-id') ||
                                    element.hasAttribute('data-video-url') ||
                                    element.hasAttribute('data-duration') ||
                                    element.closest('[data-video-id], [data-video-url]');

          // Check image src for video-related patterns
          const hasVideoSrc = isImage && element.src && (
            element.src.includes('thumbnail') ||
            element.src.includes('preview') ||
            element.src.includes('maxresdefault') ||
            element.src.includes('hqdefault') ||
            element.src.includes('mqdefault') ||
            element.src.includes('sddefault') ||
            element.src.includes('vi_webp') ||
            element.src.includes('ytimg.com')
          );

          return hasVideoIndicator ||
                 (isImage && (hasPlayButton || videoContainer || hasVideoAttributes || hasVideoSrc)) ||
                 hasVideoAttributes;
        }

        // Improved long press detection with better reliability
        function clearLongPressTimer() {
          if (longPressTimer) {
            clearTimeout(longPressTimer);
            longPressTimer = null;
          }
        }

        function calculateDistance(x1, y1, x2, y2) {
          return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
        }

        function triggerVideoThumbnailMenu(element, clientX, clientY) {
          const videoInfo = extractVideoInfo(element);
          // Update position with actual touch/click coordinates
          videoInfo.x = clientX;
          videoInfo.y = clientY;

          if (videoInfo.title || videoInfo.videoUrl) {
            try {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'videoThumbnailLongPress',
                thumbnailData: videoInfo
              }));
            } catch (err) {
              console.log('Error sending thumbnail data:', err);
            }
          }
        }

        // Add long press detection to potential video thumbnails
        function addVideoThumbnailListeners() {
          const potentialThumbnails = document.querySelectorAll('img, [class*="video"], [class*="thumbnail"], [class*="preview"]');

          potentialThumbnails.forEach(element => {
            if (isVideoThumbnail(element) && !element.hasVideoListener) {
              element.hasVideoListener = true;

              // Touch events for mobile with improved detection
              element.addEventListener('touchstart', (e) => {
                // Prevent default to avoid conflicts with other touch handlers
                e.stopPropagation();

                const touch = e.touches[0];
                touchStartTime = Date.now();
                touchStartPosition = { x: touch.clientX, y: touch.clientY };
                hasMoved = false;

                longPressTimer = setTimeout(() => {
                  if (!hasMoved) {
                    e.preventDefault();
                    triggerVideoThumbnailMenu(element, touchStartPosition.x, touchStartPosition.y);
                  }
                }, LONG_PRESS_DURATION);
              }, { passive: false });

              element.addEventListener('touchmove', (e) => {
                if (longPressTimer) {
                  const touch = e.touches[0];
                  const distance = calculateDistance(
                    touchStartPosition.x, touchStartPosition.y,
                    touch.clientX, touch.clientY
                  );

                  if (distance > MOVE_THRESHOLD) {
                    hasMoved = true;
                    clearLongPressTimer();
                  }
                }
              }, { passive: true });

              element.addEventListener('touchend', (e) => {
                clearLongPressTimer();

                // If it was a quick tap (not long press), allow normal behavior
                const touchDuration = Date.now() - touchStartTime;
                if (touchDuration < LONG_PRESS_DURATION && !hasMoved) {
                  // Let the normal click/tap behavior proceed
                  return true;
                }
              }, { passive: true });

              element.addEventListener('touchcancel', () => {
                clearLongPressTimer();
              }, { passive: true });

              // Mouse events for desktop (right-click)
              element.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                e.stopPropagation();
                triggerVideoThumbnailMenu(element, e.clientX, e.clientY);
              });

              // Optional: Add visual feedback for long press
              element.addEventListener('touchstart', () => {
                element.style.transition = 'opacity 0.1s ease';
                setTimeout(() => {
                  if (longPressTimer) {
                    element.style.opacity = '0.7';
                  }
                }, 100);
              }, { passive: true });

              element.addEventListener('touchend', () => {
                element.style.opacity = '';
                element.style.transition = '';
              }, { passive: true });
            }
          });
        }

        // Initial setup
        addVideoThumbnailListeners();

        // Debounced function to avoid excessive calls
        let observerTimeout = null;
        function debouncedAddListeners() {
          if (observerTimeout) clearTimeout(observerTimeout);
          observerTimeout = setTimeout(() => {
            addVideoThumbnailListeners();
          }, 500); // Wait 500ms after last mutation
        }

        // Re-run when new content is loaded (for dynamic content)
        const observer = new MutationObserver((mutations) => {
          let shouldUpdate = false;

          mutations.forEach(mutation => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
              // Check if any added nodes contain images or video-related elements
              mutation.addedNodes.forEach(node => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  const hasImages = node.tagName === 'IMG' || node.querySelector?.('img');
                  const hasVideoElements = node.querySelector?.('[class*="video"], [class*="thumbnail"], [class*="preview"]');
                  if (hasImages || hasVideoElements) {
                    shouldUpdate = true;
                  }
                }
              });
            }
          });

          if (shouldUpdate) {
            debouncedAddListeners();
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        // Also re-run on scroll (for lazy-loaded content)
        let scrollTimeout = null;
        window.addEventListener('scroll', () => {
          if (scrollTimeout) clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(() => {
            addVideoThumbnailListeners();
          }, 1000);
        }, { passive: true });

        // Enviar conteúdo da página para detecção de vídeo
        setTimeout(() => {
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'pageContent',
              content: document.documentElement.outerHTML
            }));
          } catch (e) {
            console.log('Error sending page content:', e);
          }
        }, 2000);

        // Ad Block básico
        const adSelectors = [
          '[id*="ad"]',
          '[class*="ad"]',
          '[id*="banner"]',
          '[class*="banner"]',
          'iframe[src*="doubleclick"]',
          'iframe[src*="googlesyndication"]',
          '.advertisement',
          '.ads',
          '.ad-container'
        ];

        const blockAds = () => {
          adSelectors.forEach(selector => {
            try {
              const elements = document.querySelectorAll(selector);
              elements.forEach(el => {
                if (el) el.style.display = 'none';
              });
            } catch (e) {
              console.log('Error blocking ads:', e);
            }
          });
        };

        // Executar bloqueio de anúncios imediatamente e após carregamento
        blockAds();
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', blockAds);
        }

        // Melhorar performance de vídeos
        const videos = document.querySelectorAll('video');
        videos.forEach(video => {
          video.preload = 'metadata';
        });

      } catch (error) {
        console.log('Error in injected script:', error);
      }
    })();
    true;
  `;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />
      
      {/* Barra de navegação com safe area */}
      <SafeAreaView edges={['left', 'right']} style={styles.navigationBarContainer}>
        <View style={styles.navigationBar}>
          <View style={styles.urlContainer}>
            <View style={styles.securityIndicator}>
              <Ionicons
                name={isSecure ? "lock-closed" : "lock-open"}
                size={16}
                color={isSecure ? "#4CAF50" : "#FF9800"}
              />
            </View>

            <TextInput
              style={styles.urlInput}
              value={url}
              onChangeText={setUrl}
              onSubmitEditing={() => navigateToUrl(url)}
              placeholder="Digite uma URL ou pesquise..."
              placeholderTextColor="#888"
              autoCapitalize="none"
              autoCorrect={false}
              selectTextOnFocus
            />

            <TouchableOpacity
              style={styles.refreshButton}
              onPress={() => webViewRef.current?.reload()}
            >
              <Ionicons name="refresh" size={20} color="#fff" />
            </TouchableOpacity>
          </View>

          <View style={styles.navigationButtons}>
            <TouchableOpacity
              style={[styles.navButton, !canGoBack && styles.navButtonDisabled]}
              onPress={() => webViewRef.current?.goBack()}
              disabled={!canGoBack}
            >
              <Ionicons name="arrow-back" size={20} color={canGoBack ? "#fff" : "#666"} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.navButton, !canGoForward && styles.navButtonDisabled]}
              onPress={() => webViewRef.current?.goForward()}
              disabled={!canGoForward}
            >
              <Ionicons name="arrow-forward" size={20} color={canGoForward ? "#fff" : "#666"} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.navButton}
              onPress={() => setShowMenu(true)}
            >
              <Ionicons name="menu" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>

      {/* Botão de download flutuante */}
      {showDownloadButton && (
        <TouchableOpacity 
          style={styles.downloadButton}
          onPress={() => onDownloadRequest && onDownloadRequest(currentUrl)}
        >
          <Ionicons name="download" size={24} color="#fff" />
        </TouchableOpacity>
      )}

      {/* WebView */}
      <WebView
        ref={webViewRef}
        source={{ uri: url }}
        style={styles.webview}
        onNavigationStateChange={handleNavigationStateChange}
        onMessage={handleMessage}
        injectedJavaScript={injectedJavaScript}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        allowsBackForwardNavigationGestures={true}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        allowsFullscreenVideo={true}
        mixedContentMode="compatibility"
        thirdPartyCookiesEnabled={true}
        sharedCookiesEnabled={true}
        cacheEnabled={true}
        userAgent="Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36"
        onError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.warn('WebView error: ', nativeEvent);
        }}
        onHttpError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.warn('WebView HTTP error: ', nativeEvent);
        }}
        onRenderProcessGone={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.warn('WebView render process gone: ', nativeEvent);
        }}
      />

      {/* Menu do navegador */}
      <Modal
        visible={showMenu}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMenu(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.menuContainer}>
            <View style={styles.menuHeader}>
              <Text style={styles.menuTitle}>Menu do Navegador</Text>
              <TouchableOpacity onPress={() => setShowMenu(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.menuContent}>
              <TouchableOpacity style={styles.menuItem} onPress={addBookmark}>
                <Ionicons name="bookmark" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Adicionar aos Favoritos</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.menuItem} 
                onPress={() => {
                  setShowMenu(false);
                  setShowBookmarks(true);
                }}
              >
                <Ionicons name="bookmarks" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Ver Favoritos</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.menuItem}>
                <Ionicons name="time" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Histórico</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.menuItem}>
                <Ionicons name="settings" size={20} color="#fff" />
                <Text style={styles.menuItemText}>Configurações</Text>
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Modal de favoritos */}
      <Modal
        visible={showBookmarks}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowBookmarks(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.bookmarksContainer}>
            <View style={styles.menuHeader}>
              <Text style={styles.menuTitle}>Favoritos</Text>
              <TouchableOpacity onPress={() => setShowBookmarks(false)}>
                <Ionicons name="close" size={24} color="#fff" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.bookmarksList}>
              {bookmarks.map((bookmark) => (
                <View key={bookmark.id} style={styles.bookmarkItem}>
                  <TouchableOpacity 
                    style={styles.bookmarkContent}
                    onPress={() => {
                      navigateToUrl(bookmark.url);
                      setShowBookmarks(false);
                    }}
                  >
                    <Text style={styles.bookmarkTitle}>{bookmark.title}</Text>
                    <Text style={styles.bookmarkUrl}>{bookmark.url}</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.deleteBookmark}
                    onPress={() => removeBookmark(bookmark.id)}
                  >
                    <Ionicons name="trash" size={16} color="#ff4444" />
                  </TouchableOpacity>
                </View>
              ))}
              
              {bookmarks.length === 0 && (
                <Text style={styles.emptyMessage}>Nenhum favorito salvo</Text>
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Video Thumbnail Context Menu */}
      <VideoThumbnailContextMenu
        visible={showThumbnailMenu}
        onClose={() => setShowThumbnailMenu(false)}
        thumbnailData={thumbnailData}
        onDownloadRequest={onDownloadRequest}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  navigationBarContainer: {
    backgroundColor: '#1a1a2e',
  },
  navigationBar: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 8,
    alignItems: 'center',
  },
  urlContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#2d2d44',
    borderRadius: 25,
    alignItems: 'center',
    paddingHorizontal: 12,
    marginRight: 10,
  },
  securityIndicator: {
    marginRight: 8,
  },
  urlInput: {
    flex: 1,
    color: '#fff',
    fontSize: 14,
    paddingVertical: 10,
  },
  refreshButton: {
    padding: 5,
  },
  navigationButtons: {
    flexDirection: 'row',
  },
  navButton: {
    padding: 8,
    marginLeft: 5,
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  downloadButton: {
    position: 'absolute',
    right: 20,
    bottom: 100,
    backgroundColor: '#6c5ce7',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1000,
  },
  webview: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  menuContainer: {
    backgroundColor: '#1a1a2e',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.6,
  },
  bookmarksContainer: {
    backgroundColor: '#1a1a2e',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.8,
  },
  menuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  menuTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  menuContent: {
    padding: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  menuItemText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 15,
  },
  bookmarksList: {
    padding: 20,
  },
  bookmarkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  bookmarkContent: {
    flex: 1,
  },
  bookmarkTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  bookmarkUrl: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  deleteBookmark: {
    padding: 10,
  },
  emptyMessage: {
    color: '#888',
    textAlign: 'center',
    marginTop: 50,
    fontSize: 16,
  },
});

export default BrowserWebView;
