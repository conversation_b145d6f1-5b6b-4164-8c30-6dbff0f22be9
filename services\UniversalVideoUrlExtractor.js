// Universal Video URL Extraction Service
// Robust video URL extraction across all platforms

class UniversalVideoUrlExtractor {
  
  // Platform-specific URL extraction strategies
  static EXTRACTION_STRATEGIES = {
    youtube: {
      patterns: [
        /youtube\.com\/watch\?v=([^&]+)/,
        /youtu\.be\/([^?]+)/,
        /youtube\.com\/embed\/([^?]+)/,
        /youtube-nocookie\.com\/embed\/([^?]+)/
      ],
      dataAttributes: ['data-video-id', 'data-youtube-id'],
      buildUrl: (id) => `https://www.youtube.com/watch?v=${id}`
    },

    vimeo: {
      patterns: [
        /vimeo\.com\/(\d+)/,
        /vimeo\.com\/video\/(\d+)/,
        /player\.vimeo\.com\/video\/(\d+)/
      ],
      dataAttributes: ['data-vimeo-id', 'data-video-id'],
      buildUrl: (id) => `https://vimeo.com/${id}`
    },

    instagram: {
      patterns: [
        /instagram\.com\/p\/([^\/]+)/,
        /instagram\.com\/reel\/([^\/]+)/,
        /instagram\.com\/tv\/([^\/]+)/
      ],
      dataAttributes: ['data-shortcode', 'data-media-id'],
      buildUrl: (id) => `https://www.instagram.com/p/${id}/`
    },

    tiktok: {
      patterns: [
        /tiktok\.com\/@[^\/]+\/video\/(\d+)/,
        /tiktok\.com\/v\/(\d+)/,
        /vm\.tiktok\.com\/([^\/]+)/
      ],
      dataAttributes: ['data-video-id', 'data-item-id'],
      buildUrl: (id) => `https://www.tiktok.com/v/${id}`
    },

    facebook: {
      patterns: [
        /facebook\.com\/.*\/videos\/(\d+)/,
        /facebook\.com\/watch\/?\?v=(\d+)/,
        /fb\.watch\/([^\/]+)/
      ],
      dataAttributes: ['data-video-id', 'data-fb-video-id'],
      buildUrl: (id) => `https://www.facebook.com/watch/?v=${id}`
    },

    twitter: {
      patterns: [
        /twitter\.com\/.*\/status\/(\d+)/,
        /x\.com\/.*\/status\/(\d+)/,
        /twitter\.com\/i\/status\/(\d+)/
      ],
      dataAttributes: ['data-tweet-id', 'data-status-id'],
      buildUrl: (id) => `https://twitter.com/i/status/${id}`
    },

    twitch: {
      patterns: [
        /twitch\.tv\/videos\/(\d+)/,
        /twitch\.tv\/.*\/clip\/([^?]+)/,
        /clips\.twitch\.tv\/([^?]+)/
      ],
      dataAttributes: ['data-video-id', 'data-clip-slug'],
      buildUrl: (id, type = 'video') => 
        type === 'clip' ? `https://clips.twitch.tv/${id}` : `https://www.twitch.tv/videos/${id}`
    },

    dailymotion: {
      patterns: [
        /dailymotion\.com\/video\/([^_]+)/,
        /dailymotion\.com\/embed\/video\/([^?]+)/,
        /dai\.ly\/([^?]+)/
      ],
      dataAttributes: ['data-video-id', 'data-xid'],
      buildUrl: (id) => `https://www.dailymotion.com/video/${id}`
    }
  };

  // Generic video URL patterns
  static GENERIC_VIDEO_PATTERNS = [
    // Direct video files
    /\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv|m4v)(\?[^"'\s]*)?$/i,
    
    // Common video hosting patterns
    /\/video\/[^\/\s"']+/i,
    /\/watch\?[^"'\s]*v=/i,
    /\/embed\/[^\/\s"']+/i,
    /\/player\/[^\/\s"']+/i,
    /\/stream\/[^\/\s"']+/i,
    
    // CDN and streaming patterns
    /\.cloudfront\.net\/.*\.(mp4|webm)/i,
    /\.amazonaws\.com\/.*\.(mp4|webm)/i,
    /streaming\..*\.(mp4|webm)/i,
    /cdn\..*\.(mp4|webm)/i
  ];

  // Extract video information from thumbnail element
  static extractVideoInfo(element, platformPatterns) {
    const info = {
      title: null,
      videoUrl: null,
      thumbnailUrl: null,
      duration: null,
      platform: null,
      videoId: null,
      x: 0,
      y: 0
    };

    // Get element position
    const rect = element.getBoundingClientRect();
    info.x = rect.left + rect.width / 2;
    info.y = rect.top + rect.height / 2;

    // Determine platform
    const hostname = window.location.hostname.toLowerCase().replace('www.', '');
    info.platform = this.detectPlatform(hostname);

    // Extract video URL using multiple strategies
    info.videoUrl = this.extractVideoUrl(element, info.platform);
    
    // Extract video ID if possible
    info.videoId = this.extractVideoId(info.videoUrl, info.platform);

    // Extract title
    info.title = this.extractTitle(element, platformPatterns);

    // Extract thumbnail URL
    info.thumbnailUrl = this.extractThumbnailUrl(element);

    // Extract duration
    info.duration = this.extractDuration(element, platformPatterns);

    return info;
  }

  // Detect platform from hostname
  static detectPlatform(hostname) {
    for (const [platform, config] of Object.entries(this.EXTRACTION_STRATEGIES)) {
      if (hostname.includes(platform) || 
          (platform === 'twitter' && hostname.includes('x.com'))) {
        return platform;
      }
    }
    return 'generic';
  }

  // Extract video URL using multiple strategies
  static extractVideoUrl(element, platform) {
    // Strategy 1: Direct link extraction
    const directUrl = this.extractDirectLink(element, platform);
    if (directUrl) return directUrl;

    // Strategy 2: Data attribute extraction
    const dataUrl = this.extractFromDataAttributes(element, platform);
    if (dataUrl) return dataUrl;

    // Strategy 3: Embedded iframe extraction
    const iframeUrl = this.extractFromIframe(element);
    if (iframeUrl) return iframeUrl;

    // Strategy 4: JSON-LD structured data
    const structuredUrl = this.extractFromStructuredData();
    if (structuredUrl) return structuredUrl;

    // Strategy 5: Meta tags
    const metaUrl = this.extractFromMetaTags();
    if (metaUrl) return metaUrl;

    // Strategy 6: Script tag analysis
    const scriptUrl = this.extractFromScripts(element);
    if (scriptUrl) return scriptUrl;

    return null;
  }

  // Extract direct link from element or its parents
  static extractDirectLink(element, platform) {
    const searchElements = [
      element,
      element.parentElement,
      element.parentElement?.parentElement,
      element.closest('a'),
      element.closest('[href]')
    ].filter(Boolean);

    for (const el of searchElements) {
      const href = el.href || el.getAttribute('href');
      if (href && this.isValidVideoUrl(href, platform)) {
        return href;
      }
    }

    return null;
  }

  // Extract from data attributes
  static extractFromDataAttributes(element, platform) {
    const strategy = this.EXTRACTION_STRATEGIES[platform];
    if (!strategy) return null;

    const searchElements = [
      element,
      element.parentElement,
      element.parentElement?.parentElement,
      element.closest('[data-video-id], [data-video-url]')
    ].filter(Boolean);

    for (const el of searchElements) {
      // Platform-specific data attributes
      if (strategy.dataAttributes) {
        for (const attr of strategy.dataAttributes) {
          const value = el.getAttribute(attr);
          if (value && strategy.buildUrl) {
            return strategy.buildUrl(value);
          }
        }
      }

      // Generic data attributes
      const genericAttrs = [
        'data-video-url', 'data-src', 'data-href',
        'data-video-id', 'data-embed-url', 'data-player-url'
      ];

      for (const attr of genericAttrs) {
        const value = el.getAttribute(attr);
        if (value && this.isValidVideoUrl(value, platform)) {
          return value.startsWith('http') ? value : this.buildAbsoluteUrl(value);
        }
      }
    }

    return null;
  }

  // Extract from iframe elements
  static extractFromIframe(element) {
    const iframes = [
      element.querySelector('iframe'),
      element.parentElement?.querySelector('iframe'),
      element.closest('div')?.querySelector('iframe')
    ].filter(Boolean);

    for (const iframe of iframes) {
      const src = iframe.src || iframe.getAttribute('data-src');
      if (src && this.isValidVideoUrl(src)) {
        return src;
      }
    }

    return null;
  }

  // Extract from JSON-LD structured data
  static extractFromStructuredData() {
    const scripts = document.querySelectorAll('script[type="application/ld+json"]');
    
    for (const script of scripts) {
      try {
        const data = JSON.parse(script.textContent);
        const videoUrl = this.findVideoUrlInObject(data);
        if (videoUrl) return videoUrl;
      } catch (e) {
        // Ignore invalid JSON
      }
    }

    return null;
  }

  // Extract from meta tags
  static extractFromMetaTags() {
    const metaSelectors = [
      'meta[property="og:video"]',
      'meta[property="og:video:url"]',
      'meta[property="og:video:secure_url"]',
      'meta[name="twitter:player"]',
      'meta[property="video:url"]'
    ];

    for (const selector of metaSelectors) {
      const meta = document.querySelector(selector);
      if (meta) {
        const content = meta.getAttribute('content');
        if (content && this.isValidVideoUrl(content)) {
          return content;
        }
      }
    }

    return null;
  }

  // Extract from script tags (for embedded players)
  static extractFromScripts(element) {
    // Look for nearby script tags that might contain video URLs
    const container = element.closest('div, article, section') || element.parentElement;
    const scripts = container?.querySelectorAll('script') || [];

    for (const script of scripts) {
      const content = script.textContent || script.innerHTML;
      if (content) {
        // Look for video URLs in script content
        const urlMatch = content.match(/"(https?:\/\/[^"]*(?:video|watch|embed|player)[^"]*)"/) ||
                        content.match(/'(https?:\/\/[^']*(?:video|watch|embed|player)[^']*)'/) ||
                        content.match(/url:\s*["']([^"']*(?:video|watch|embed)[^"']*)/);
        
        if (urlMatch && this.isValidVideoUrl(urlMatch[1])) {
          return urlMatch[1];
        }
      }
    }

    return null;
  }

  // Enhanced title extraction
  static extractTitle(element, platformPatterns) {
    const titleSources = [
      // Element attributes
      () => element.getAttribute('title'),
      () => element.getAttribute('alt'),
      () => element.getAttribute('aria-label'),
      
      // Platform-specific selectors
      () => {
        if (platformPatterns?.titleSelectors) {
          for (const selector of platformPatterns.titleSelectors) {
            const titleEl = element.querySelector(selector) || 
                           element.closest('div, article')?.querySelector(selector);
            if (titleEl) {
              return titleEl.textContent?.trim();
            }
          }
        }
        return null;
      },
      
      // Generic title selectors
      () => {
        const selectors = [
          'h1', 'h2', 'h3', '.title', '.video-title',
          '.media-title', '.content-title', '.headline'
        ];
        
        const container = element.closest('div, article, section') || element.parentElement;
        for (const selector of selectors) {
          const titleEl = container?.querySelector(selector);
          if (titleEl) {
            const text = titleEl.textContent?.trim();
            if (text && text.length > 3 && text.length < 200) {
              return text;
            }
          }
        }
        return null;
      },
      
      // Link text
      () => {
        const link = element.closest('a');
        if (link) {
          const text = link.textContent?.trim();
          if (text && text.length > 3 && text.length < 200) {
            return text;
          }
        }
        return null;
      }
    ];

    for (const source of titleSources) {
      try {
        const title = source();
        if (title) return title;
      } catch (e) {
        // Continue to next source
      }
    }

    return 'Video';
  }

  // Extract thumbnail URL
  static extractThumbnailUrl(element) {
    if (element.tagName === 'IMG') {
      return element.src || element.getAttribute('data-src');
    }

    const img = element.querySelector('img');
    if (img) {
      return img.src || img.getAttribute('data-src');
    }

    // Check for background images
    const style = window.getComputedStyle(element);
    const bgImage = style.backgroundImage;
    if (bgImage && bgImage !== 'none') {
      const match = bgImage.match(/url\(["']?([^"')]+)["']?\)/);
      if (match) return match[1];
    }

    return null;
  }

  // Extract duration
  static extractDuration(element, platformPatterns) {
    const durationSources = [
      // Platform-specific selectors
      () => {
        if (platformPatterns?.durationSelectors) {
          for (const selector of platformPatterns.durationSelectors) {
            const durationEl = element.querySelector(selector) || 
                              element.closest('div')?.querySelector(selector);
            if (durationEl) {
              return durationEl.textContent?.trim();
            }
          }
        }
        return null;
      },
      
      // Generic duration selectors
      () => {
        const selectors = [
          '.duration', '.time', '.length', '.runtime',
          '[data-duration]', '.video-duration'
        ];
        
        const container = element.closest('div, article') || element.parentElement;
        for (const selector of selectors) {
          const durationEl = container?.querySelector(selector);
          if (durationEl) {
            const text = durationEl.textContent?.trim() || 
                        durationEl.getAttribute('data-duration');
            if (text && text.match(/\d+:\d+/)) {
              return text;
            }
          }
        }
        return null;
      }
    ];

    for (const source of durationSources) {
      try {
        const duration = source();
        if (duration) return duration;
      } catch (e) {
        // Continue to next source
      }
    }

    return null;
  }

  // Utility functions
  static isValidVideoUrl(url, platform = null) {
    if (!url || typeof url !== 'string') return false;

    // Platform-specific validation
    if (platform && this.EXTRACTION_STRATEGIES[platform]) {
      const strategy = this.EXTRACTION_STRATEGIES[platform];
      return strategy.patterns.some(pattern => pattern.test(url));
    }

    // Generic validation
    return this.GENERIC_VIDEO_PATTERNS.some(pattern => pattern.test(url)) ||
           url.includes('video') || url.includes('watch') || url.includes('embed');
  }

  static extractVideoId(url, platform) {
    if (!url || !platform) return null;

    const strategy = this.EXTRACTION_STRATEGIES[platform];
    if (!strategy) return null;

    for (const pattern of strategy.patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }

    return null;
  }

  static buildAbsoluteUrl(relativeUrl) {
    if (relativeUrl.startsWith('http')) return relativeUrl;
    if (relativeUrl.startsWith('//')) return `https:${relativeUrl}`;
    if (relativeUrl.startsWith('/')) return `${window.location.origin}${relativeUrl}`;
    return `${window.location.href}/${relativeUrl}`;
  }

  static findVideoUrlInObject(obj) {
    if (typeof obj !== 'object' || obj === null) return null;

    // Check common video properties
    const videoProps = ['contentUrl', 'embedUrl', 'url', 'video', 'videoUrl'];
    for (const prop of videoProps) {
      if (obj[prop] && this.isValidVideoUrl(obj[prop])) {
        return obj[prop];
      }
    }

    // Recursively search nested objects
    for (const value of Object.values(obj)) {
      if (typeof value === 'object') {
        const result = this.findVideoUrlInObject(value);
        if (result) return result;
      }
    }

    return null;
  }
}

export default UniversalVideoUrlExtractor;
