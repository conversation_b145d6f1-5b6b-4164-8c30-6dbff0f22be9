import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Modal,
  ScrollView,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

const TAB_GROUP_COLORS = [
  { name: 'Azul', color: '#4285F4', lightColor: '#E3F2FD' },
  { name: 'Verde', color: '#34A853', lightColor: '#E8F5E8' },
  { name: 'Vermel<PERSON>', color: '#EA4335', lightColor: '#FFEBEE' },
  { name: '<PERSON><PERSON>', color: '#FBBC04', lightColor: '#FFFDE7' },
  { name: 'Roxo', color: '#9C27B0', lightColor: '#F3E5F5' },
  { name: 'Laranja', color: '#FF9800', lightColor: '#FFF3E0' },
  { name: '<PERSON>', color: '#E91E63', lightColor: '#FCE4EC' },
  { name: '<PERSON><PERSON>', color: '#00BCD4', lightColor: '#E0F2F1' }
];

const TabGroup = ({ 
  group, 
  tabs, 
  onToggleCollapse, 
  onRenameGroup, 
  onChangeGroupColor, 
  onDeleteGroup,
  onTabPress,
  onCloseTab,
  activeTabId 
}) => {
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [showColorModal, setShowColorModal] = useState(false);
  const [newGroupName, setNewGroupName] = useState(group.name);

  const groupTabs = tabs.filter(tab => tab.groupId === group.id);
  const groupColor = TAB_GROUP_COLORS.find(c => c.color === group.color) || TAB_GROUP_COLORS[0];

  const handleRename = () => {
    if (newGroupName.trim()) {
      onRenameGroup(group.id, newGroupName.trim());
      setShowRenameModal(false);
    }
  };

  const handleColorChange = (color) => {
    onChangeGroupColor(group.id, color.color);
    setShowColorModal(false);
  };

  return (
    <View style={styles.groupContainer}>
      {/* Group Header */}
      <View style={[styles.groupHeader, { backgroundColor: groupColor.lightColor }]}>
        <TouchableOpacity
          style={styles.collapseButton}
          onPress={() => onToggleCollapse(group.id)}
        >
          <Ionicons
            name={group.collapsed ? "chevron-forward" : "chevron-down"}
            size={16}
            color={groupColor.color}
          />
        </TouchableOpacity>

        <View style={[styles.groupColorIndicator, { backgroundColor: groupColor.color }]} />

        <TouchableOpacity
          style={styles.groupNameContainer}
          onLongPress={() => setShowRenameModal(true)}
        >
          <Text style={[styles.groupName, { color: groupColor.color }]}>
            {group.name}
          </Text>
          <Text style={styles.groupTabCount}>
            ({groupTabs.length} {groupTabs.length === 1 ? 'aba' : 'abas'})
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.groupMenuButton}
          onPress={() => setShowColorModal(true)}
        >
          <Ionicons name="color-palette" size={16} color={groupColor.color} />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.groupMenuButton}
          onPress={() => onDeleteGroup(group.id)}
        >
          <Ionicons name="close" size={16} color={groupColor.color} />
        </TouchableOpacity>
      </View>

      {/* Group Tabs */}
      {!group.collapsed && (
        <View style={styles.groupTabs}>
          {groupTabs.map((tab) => (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.groupTab,
                activeTabId === tab.id && styles.activeGroupTab,
                { borderLeftColor: groupColor.color }
              ]}
              onPress={() => onTabPress(tab.id)}
            >
              <View style={styles.tabContent}>
                <Text style={[
                  styles.tabTitle,
                  activeTabId === tab.id && styles.activeTabTitle
                ]}>
                  {tab.title.length > 20 ? tab.title.substring(0, 20) + '...' : tab.title}
                </Text>
                <Text style={styles.tabUrl}>
                  {tab.url.length > 30 ? tab.url.substring(0, 30) + '...' : tab.url}
                </Text>
              </View>

              <TouchableOpacity
                style={styles.closeTabButton}
                onPress={() => onCloseTab(tab.id)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="close" size={14} color="#888" />
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Rename Modal */}
      <Modal
        visible={showRenameModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowRenameModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Renomear Grupo</Text>
            
            <TextInput
              style={styles.modalInput}
              value={newGroupName}
              onChangeText={setNewGroupName}
              placeholder="Nome do grupo"
              placeholderTextColor="#888"
              autoFocus
              selectTextOnFocus
            />

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowRenameModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancelar</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleRename}
              >
                <Text style={styles.confirmButtonText}>Salvar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Color Selection Modal */}
      <Modal
        visible={showColorModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowColorModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Escolher Cor do Grupo</Text>
            
            <ScrollView style={styles.colorGrid}>
              {TAB_GROUP_COLORS.map((color, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.colorOption,
                    { backgroundColor: color.lightColor },
                    group.color === color.color && styles.selectedColorOption
                  ]}
                  onPress={() => handleColorChange(color)}
                >
                  <View style={[styles.colorCircle, { backgroundColor: color.color }]} />
                  <Text style={[styles.colorName, { color: color.color }]}>
                    {color.name}
                  </Text>
                  {group.color === color.color && (
                    <Ionicons name="checkmark" size={20} color={color.color} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setShowColorModal(false)}
            >
              <Text style={styles.cancelButtonText}>Fechar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  groupContainer: {
    marginBottom: 8,
  },
  groupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  collapseButton: {
    padding: 4,
    marginRight: 8,
  },
  groupColorIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  groupNameContainer: {
    flex: 1,
  },
  groupName: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  groupTabCount: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  groupMenuButton: {
    padding: 4,
    marginLeft: 8,
  },
  groupTabs: {
    paddingLeft: 20,
  },
  groupTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginVertical: 2,
    backgroundColor: '#2d2d44',
    borderRadius: 6,
    borderLeftWidth: 3,
    marginHorizontal: 4,
  },
  activeGroupTab: {
    backgroundColor: '#3d3d54',
  },
  tabContent: {
    flex: 1,
  },
  tabTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  activeTabTitle: {
    color: '#6c5ce7',
  },
  tabUrl: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  closeTabButton: {
    padding: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#1a1a2e',
    borderRadius: 12,
    padding: 20,
    width: width * 0.85,
    maxHeight: '80%',
  },
  modalTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  modalInput: {
    backgroundColor: '#2d2d44',
    color: '#fff',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#666',
    marginRight: 8,
  },
  confirmButton: {
    backgroundColor: '#6c5ce7',
    marginLeft: 8,
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  colorGrid: {
    maxHeight: 300,
    marginBottom: 16,
  },
  colorOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColorOption: {
    borderColor: '#6c5ce7',
  },
  colorCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 12,
  },
  colorName: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
});

export default TabGroup;
export { TAB_GROUP_COLORS };
